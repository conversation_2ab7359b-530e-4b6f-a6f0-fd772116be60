@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
  scroll-behavior: smooth;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Forever Fest gradient backgrounds */
  .bg-forever-fest-gradient {
    background: linear-gradient(180deg, #101048 0%, #DE1ACE 100%);
  }

  /* Forever Fest title gradient */
  .bg-forever-fest-title-gradient {
    background-image: linear-gradient(45deg, #DE1ACE, #FCADE7, #DE1ACE);
  }

  /* Make League Gothic available for SVG elements */
  .league-gothic-svg {
    font-family: var(--font-league-gothic), 'League Gothic', Arial, Helvetica, sans-serif;
  }

  /* Make Alex Brush available for SVG elements */
  .alex-brush-svg {
    font-family: var(--font-alex-brush), 'Alex Brush', cursive;
  }

  /* Custom pulsing glow animation for RSVP button */
  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(184, 21, 158, 0.6), 0 0 40px rgba(184, 21, 158, 0.4), 0 0 60px rgba(184, 21, 158, 0.2);
    }
    50% {
      box-shadow: 0 0 30px rgba(184, 21, 158, 0.8), 0 0 60px rgba(184, 21, 158, 0.6), 0 0 90px rgba(184, 21, 158, 0.4);
    }
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Subtle floating animation for extra attention */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-3px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
}

/* Minimal CSS variables - only what's needed for shadcn/ui components */
@layer base {
  :root {
    /* Only essential variables for shadcn components */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --muted-foreground: 0 0% 63.9%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
